:root {
    --red_dark: #561010;
    --red_pop: #F13F3F;
    --light-gray: #eaeaea;
}

body, html {
    overflow-x: clip !important;
    width: 100vw !important;
}

.limited-width-content {
    max-width: 1200px !important;
    margin: auto !important;
}

.limited-width-content-xl {
    max-width: 1400px !important;
    margin: auto !important;
}

.space-from-top {
    margin-top: 10vw !important;
}

.space-from-top-m {
    margin-top: 6vw !important;
}

.space-from-bottom {
    margin-bottom: 6vh !important;
}

.space-from-bottom-xl {
    margin-bottom: 10vw !important;
}


.stretch-text {
    text-align: justify;
    letter-spacing: 3px;
}

.footer-links {
    font-size: 14px !important;
    margin-top: 4px;
    margin-bottom: 4px;
    display: block;
    color: #7a7a7a;
    text-decoration: none;
}


.learn-links {
    font-size: 14px !important;
    margin-top: 4px;
    margin-bottom: 4px;
    display: block;
    color: #0e0e0e;
    text-decoration: none;
}


.footer-links:hover {
    color: var(--red_pop) !important;
}

.social-link:hover {
    color: var(--red_pop) !important;
}

.main-link-item {
    text-decoration: none;
    padding-bottom: 8px !important;
    color: #000 !important;
    font-size: 24px !important;
    display: block;
}

#footer {
    background-color: #f6f6f6;
}

.carousel-indicators {
    list-style-type: none;
}

.carousel-item {
    overflow: hidden;
    width: 100%;
}

#demo-req-form {
    max-width: 600px !important;
}

#demo-request-text {
    padding-top: 12vh !important;
    padding-bottom: 8vh !important;
}

.dark-red-text {
    color: var(--red_dark) !important;
}

.pop-red-text {
    color: var(--red_pop) !important;
}

.pricing-card {
    border-radius: 8px;
    border-style: solid;
    border-color: var(--light-gray);
    border-width: 2px;
    column-fill: auto;
    transition: 0.5s;
}

.pricing-card:hover {
    box-shadow: 0px 0px 10px #6a6a6a !important;
}

.feature-card {
    border-radius: 8px;
    border-style: solid;
    border-color: var(--light-gray);
    border-width: 2px;
    column-fill: auto;
    transition: 0.3s;
    position: relative;
}

.feature-card:hover {
    box-shadow: 0px 0px 10px #e6d8d8 !important;
}

#transition-from-red {
    background: url(./Static/Illustrations/arkaplan_2.svg);
    background-size: cover;
    background-position: center bottom;
}

#transition-to-red {
    background: url(./Static/Illustrations/arkaplan.svg);
    background-size: cover;
}

#transition-from-red-alt {
    background: url(./Static/Illustrations/arkaplan.svg);
    background-size: cover;
    transform: scaleY(-1);
}

#transition-to-gray {
    background: url(./Static/Illustrations/arkaplan_3.svg);
    background-size: cover;
}

#transition-from-gray {
    background: url(./Static/Illustrations/arkaplan_3.svg);
    background-size: cover;
    transform: scaleY(-1);
}

.dark-gray-bg {
    background-color: #5B5B5B;
    color: #f5f5f5;
}

.dark-red-bg {
    background-color: #B62826;
    color: #f5f5f5;
}

.lrn-more-btn {
    border-top-left-radius: 0%;
    border-top-right-radius: 0%;
    position: absolute;
    bottom: 0;
    width: 100%;
}

.feature-info-card {
    background-color: #ffffff;
    border-radius: 24px;
    padding: 10px;
    margin: 10px;
    margin-bottom: 30px;
}

.landing-info-card {
    background-color: #f1f1f1;
    border-radius: 24px;
    padding: 10px;
    margin: 10px;
    margin-bottom: 30px;
}

.landing-info-card-alt {
    background-color: #222222;
    border-radius: 24px;
    padding: 10px;
    margin: 10px;
    margin-bottom: 30px;
}

.feature-info-card-text {
    max-width: 1500px;
    color: #000;
}

.demo-img {
    max-height: 300px;
}

.client-logo {
    max-height: 80px !important;
    max-width: 120px !important;
    width: 100%;
}

.bootstrap-icon {
    font-size: 100px !important;
    color: #050505 !important;
    animation-duration: 10s;
    animation-name: icon-animation;
    animation-iteration-count: infinite;
}

@keyframes icon-animation {
    0%, 100% {
        transform: scale(1) translateX(0);
    }
    50% {
        transform: scale(1.2) translateX(0);
    }
}

.bootstrap-icon-stable {
    font-size: 100px !important;
    color: #050505 !important;
}