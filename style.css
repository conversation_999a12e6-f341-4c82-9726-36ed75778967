/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&display=swap');

/* Modern CSS Variables */
:root {
    /* Primary Colors - Modern Purple/Blue Gradient Palette */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);

    /* Solid Colors */
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --accent-color: #4facfe;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;

    /* Neutral Colors */
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-secondary: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;

    /* Spacing */
    --section-padding: 120px 0;
    --container-padding: 0 24px;

    /* Border Radius */
    --radius-sm: 8px;
    --radius-md: 16px;
    --radius-lg: 24px;
    --radius-xl: 32px;

    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

    /* Glassmorphism */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--white);
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Container System */
.container-custom {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--container-padding);
}

.container-wide {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--container-padding);
}

.container-narrow {
    max-width: 800px;
    margin: 0 auto;
    padding: var(--container-padding);
}

/* Legacy Support */
.limited-width-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--container-padding);
}

.limited-width-content-xl {
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--container-padding);
}

/* Modern Spacing System */
.section-padding {
    padding: var(--section-padding);
}

.py-section {
    padding-top: 120px;
    padding-bottom: 120px;
}

.pt-section {
    padding-top: 120px;
}

.pb-section {
    padding-bottom: 120px;
}

/* Legacy Spacing */
.space-from-top {
    margin-top: 120px;
}

.space-from-top-m {
    margin-top: 80px;
}

.space-from-bottom {
    margin-bottom: 80px;
}

.space-from-bottom-xl {
    margin-bottom: 120px;
}

/* Typography Styles */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-secondary);
    font-weight: 700;
    line-height: 1.2;
    color: var(--gray-900);
    margin-bottom: 1rem;
}

.display-1 {
    font-size: 4.5rem;
    font-weight: 900;
    line-height: 1.1;
}

.display-2 {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.1;
}

.display-3 {
    font-size: 2.75rem;
    font-weight: 700;
    line-height: 1.2;
}

.lead {
    font-size: 1.25rem;
    font-weight: 400;
    color: var(--gray-600);
    line-height: 1.6;
}

.text-gradient {
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Modern Button System */
.btn-modern {
    padding: 16px 32px;
    border-radius: var(--radius-md);
    font-weight: 600;
    font-size: 1rem;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    transition: all var(--transition-normal);
    border: none;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--primary-gradient);
    color: var(--white);
    box-shadow: var(--shadow-lg);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    color: var(--white);
}

.btn-secondary {
    background: var(--white);
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    box-shadow: var(--shadow-md);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-glass {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border: 1px solid var(--glass-border);
    color: var(--white);
    box-shadow: var(--glass-shadow);
}

.btn-glass:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    color: var(--white);
}

/* Modern Card System */
.card-modern {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.card-modern:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
}

.card-glass {
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-lg);
    padding: 2rem;
    box-shadow: var(--glass-shadow);
    transition: all var(--transition-normal);
}

.card-glass:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-4px);
}

.card-gradient {
    background: var(--primary-gradient);
    color: var(--white);
    border-radius: var(--radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-xl);
    transition: all var(--transition-normal);
}

.card-gradient:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-2xl);
}

/* Modern Navbar */
.navbar-modern {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--gray-200);
    padding: 1rem 0;
    transition: all var(--transition-normal);
}

.navbar-modern.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-lg);
}

.navbar-brand img {
    height: 40px;
    transition: all var(--transition-normal);
}

.nav-link {
    font-weight: 500;
    color: var(--gray-700);
    transition: all var(--transition-fast);
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-gradient);
    transition: width var(--transition-normal);
}

.nav-link:hover::after {
    width: 100%;
}

/* Hero Section */
.hero-section {
    background: var(--primary-gradient);
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="%23ffffff" stop-opacity="0.1"/><stop offset="100%" stop-color="%23ffffff" stop-opacity="0"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    position: relative;
    z-index: 2;
    color: var(--white);
}

.hero-title {
    font-size: 4rem;
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 1.5rem;
}

.hero-subtitle {
    font-size: 1.5rem;
    font-weight: 400;
    opacity: 0.9;
    margin-bottom: 2rem;
    max-width: 600px;
}

/* Animated Elements */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-float {
    animation: float 6s ease-in-out infinite;
}

.animate-fade-in-up {
    animation: fadeInUp 0.8s ease-out;
}

.animate-slide-in-left {
    animation: slideInLeft 0.8s ease-out;
}

.animate-slide-in-right {
    animation: slideInRight 0.8s ease-out;
}

/* Features Section */
.features-section {
    background: var(--gray-50);
    padding: var(--section-padding);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    font-size: 2rem;
    color: var(--white);
    transition: all var(--transition-normal);
}

.feature-icon:hover {
    transform: scale(1.1) rotate(5deg);
}

/* Pricing Cards */
.pricing-section {
    background: var(--white);
    padding: var(--section-padding);
}

.pricing-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: 3rem 2rem;
    box-shadow: var(--shadow-lg);
    border: 2px solid var(--gray-200);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
    height: 100%;
}

.pricing-card:hover {
    transform: translateY(-12px);
    box-shadow: var(--shadow-2xl);
    border-color: var(--primary-color);
}

.pricing-card.featured {
    background: var(--primary-gradient);
    color: var(--white);
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.pricing-card.featured:hover {
    transform: scale(1.08) translateY(-12px);
}

.pricing-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--primary-gradient);
}

.pricing-card.featured::before {
    background: var(--accent-gradient);
}

.pricing-badge {
    position: absolute;
    top: 20px;
    right: 20px;
    background: var(--success-color);
    color: var(--white);
    padding: 8px 16px;
    border-radius: var(--radius-md);
    font-size: 0.875rem;
    font-weight: 600;
}

.pricing-price {
    font-size: 3rem;
    font-weight: 900;
    color: var(--primary-color);
    margin: 1rem 0;
}

.pricing-card.featured .pricing-price {
    color: var(--white);
}

.pricing-features {
    list-style: none;
    padding: 0;
    margin: 2rem 0;
}

.pricing-features li {
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    gap: 12px;
}

.pricing-card.featured .pricing-features li {
    border-bottom-color: rgba(255, 255, 255, 0.2);
}

.pricing-features li::before {
    content: '✓';
    color: var(--success-color);
    font-weight: bold;
    font-size: 1.2rem;
}

.pricing-card.featured .pricing-features li::before {
    color: var(--white);
}

/* Process/Timeline Section */
.process-section {
    background: var(--gray-900);
    color: var(--white);
    padding: var(--section-padding);
    position: relative;
}

.process-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23ffffff" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
}

.process-step {
    position: relative;
    padding: 2rem;
    margin-bottom: 2rem;
}

.process-number {
    width: 60px;
    height: 60px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
}

.process-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: var(--radius-lg);
    padding: 2rem;
    transition: all var(--transition-normal);
}

.process-card:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(20px);
}

/* Testimonials Section */
.testimonials-section {
    background: var(--gray-50);
    padding: var(--section-padding);
}

.testimonial-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: 2.5rem;
    box-shadow: var(--shadow-lg);
    text-align: center;
    transition: all var(--transition-normal);
    position: relative;
}

.testimonial-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
}

.testimonial-card::before {
    content: '"';
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 4rem;
    color: var(--primary-color);
    font-family: serif;
}

.testimonial-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin: 0 auto 1rem;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 2rem;
    font-weight: bold;
}

.testimonial-stars {
    color: var(--warning-color);
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

/* Stats Section */
.stats-section {
    background: var(--primary-gradient);
    color: var(--white);
    padding: var(--section-padding);
    text-align: center;
}

.stat-item {
    padding: 2rem 1rem;
}

.stat-number {
    font-size: 3.5rem;
    font-weight: 900;
    margin-bottom: 0.5rem;
    display: block;
}

.stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 500;
}

/* CTA Section */
.cta-section {
    background: var(--dark-gradient);
    color: var(--white);
    padding: var(--section-padding);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.cta-content {
    position: relative;
    z-index: 2;
}

/* Modern Footer */
.footer-modern {
    background: var(--gray-900);
    color: var(--gray-300);
    padding: 4rem 0 2rem;
}

.footer-section h4 {
    color: var(--white);
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.footer-links {
    color: var(--gray-400);
    text-decoration: none;
    display: block;
    padding: 0.5rem 0;
    transition: all var(--transition-fast);
    font-size: 0.95rem;
}

.footer-links:hover {
    color: var(--primary-color);
    transform: translateX(4px);
}

.footer-social {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-link {
    width: 40px;
    height: 40px;
    background: var(--gray-800);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-400);
    transition: all var(--transition-normal);
    text-decoration: none;
}

.social-link:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: translateY(-2px);
}

.footer-bottom {
    border-top: 1px solid var(--gray-800);
    padding-top: 2rem;
    margin-top: 3rem;
    text-align: center;
    color: var(--gray-500);
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

.d-flex {
    display: flex;
}

.align-items-center {
    align-items: center;
}

.justify-content-center {
    justify-content: center;
}

.justify-content-between {
    justify-content: space-between;
}

.gap-1 {
    gap: 0.5rem;
}

.gap-2 {
    gap: 1rem;
}

.gap-3 {
    gap: 1.5rem;
}

.gap-4 {
    gap: 2rem;
}

.mb-0 {
    margin-bottom: 0;
}

.mb-1 {
    margin-bottom: 0.5rem;
}

.mb-2 {
    margin-bottom: 1rem;
}

.mb-3 {
    margin-bottom: 1.5rem;
}

.mb-4 {
    margin-bottom: 2rem;
}

.mb-5 {
    margin-bottom: 3rem;
}

.mt-0 {
    margin-top: 0;
}

.mt-1 {
    margin-top: 0.5rem;
}

.mt-2 {
    margin-top: 1rem;
}

.mt-3 {
    margin-top: 1.5rem;
}

.mt-4 {
    margin-top: 2rem;
}

.mt-5 {
    margin-top: 3rem;
}

/* Legacy Support Classes */
.dark-red-text {
    color: var(--primary-color);
}

.pop-red-text {
    color: var(--secondary-color);
}

.dark-gray-bg {
    background-color: var(--gray-800);
    color: var(--white);
}

.dark-red-bg {
    background: var(--primary-gradient);
    color: var(--white);
}

.feature-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-lg);
    border: 2px solid var(--gray-200);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
    border-color: var(--primary-color);
}

.feature-info-card {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: 2rem;
    margin: 1rem;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.feature-info-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.landing-info-card {
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    padding: 2rem;
    margin: 1rem;
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
}

.landing-info-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.landing-info-card-alt {
    background: var(--gray-900);
    color: var(--white);
    border-radius: var(--radius-lg);
    padding: 2rem;
    margin: 1rem;
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-normal);
}

.landing-info-card-alt:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.feature-info-card-text {
    max-width: 100%;
    color: var(--gray-800);
}

.demo-img {
    max-height: 400px;
    width: 100%;
    object-fit: contain;
}

.client-logo {
    max-height: 80px;
    max-width: 120px;
    width: 100%;
    object-fit: contain;
    filter: grayscale(100%);
    opacity: 0.7;
    transition: all var(--transition-normal);
}

.client-logo:hover {
    filter: grayscale(0%);
    opacity: 1;
    transform: scale(1.05);
}

.bootstrap-icon {
    font-size: 4rem;
    color: var(--primary-color);
    transition: all var(--transition-normal);
}

.bootstrap-icon:hover {
    color: var(--secondary-color);
    transform: scale(1.1);
}

.bootstrap-icon-stable {
    font-size: 4rem;
    color: var(--primary-color);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .container-custom,
    .limited-width-content {
        max-width: 960px;
    }

    .hero-title {
        font-size: 3.5rem;
    }

    .display-1 {
        font-size: 4rem;
    }

    .display-2 {
        font-size: 3rem;
    }
}

@media (max-width: 992px) {
    .container-custom,
    .limited-width-content {
        max-width: 720px;
    }

    .hero-title {
        font-size: 3rem;
    }

    .hero-subtitle {
        font-size: 1.25rem;
    }

    .display-1 {
        font-size: 3.5rem;
    }

    .display-2 {
        font-size: 2.5rem;
    }

    .display-3 {
        font-size: 2rem;
    }

    .pricing-card.featured {
        transform: none;
    }

    .pricing-card.featured:hover {
        transform: translateY(-12px);
    }

    .section-padding,
    .py-section {
        padding: 80px 0;
    }

    .pt-section {
        padding-top: 80px;
    }

    .pb-section {
        padding-bottom: 80px;
    }
}

@media (max-width: 768px) {
    .container-custom,
    .limited-width-content {
        max-width: 540px;
        padding: 0 20px;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .display-1 {
        font-size: 3rem;
    }

    .display-2 {
        font-size: 2.25rem;
    }

    .display-3 {
        font-size: 1.75rem;
    }

    .btn-modern {
        padding: 14px 24px;
        font-size: 0.95rem;
    }

    .card-modern,
    .card-glass,
    .card-gradient {
        padding: 1.5rem;
    }

    .pricing-card {
        padding: 2rem 1.5rem;
    }

    .section-padding,
    .py-section {
        padding: 60px 0;
    }

    .pt-section {
        padding-top: 60px;
    }

    .pb-section {
        padding-bottom: 60px;
    }

    .space-from-top {
        margin-top: 80px;
    }

    .space-from-top-m {
        margin-top: 60px;
    }

    .space-from-bottom {
        margin-bottom: 60px;
    }

    .space-from-bottom-xl {
        margin-bottom: 80px;
    }
}

@media (max-width: 576px) {
    .container-custom,
    .limited-width-content {
        max-width: 100%;
        padding: 0 16px;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .display-1 {
        font-size: 2.5rem;
    }

    .display-2 {
        font-size: 2rem;
    }

    .display-3 {
        font-size: 1.5rem;
    }

    .btn-modern {
        padding: 12px 20px;
        font-size: 0.9rem;
        width: 100%;
        margin-bottom: 1rem;
    }

    .card-modern,
    .card-glass,
    .card-gradient {
        padding: 1.25rem;
    }

    .pricing-card {
        padding: 1.5rem 1rem;
    }

    .section-padding,
    .py-section {
        padding: 40px 0;
    }

    .pt-section {
        padding-top: 40px;
    }

    .pb-section {
        padding-bottom: 40px;
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .testimonial-card {
        padding: 1.5rem;
    }

    .process-card {
        padding: 1.5rem;
    }

    .process-number {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
}